import { useEffect, useState } from "react";
import styled from "styled-components";
import profileMan from "../../../assets/images/profile/profileMan.svg";
import Container from "../../../components/Container";
import Fieldset from "../../../components/Fiedlset/Fieldset";
import Legend from "../../../components/Fiedlset/Legend";
import Image from "../../../components/Image";
import Button from "../../../components/Inputs/Button";
import Label from "../../../components/Inputs/Label";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";

import copyIconHover from "../../../assets/images/profile/copyIconHover.svg";
import copyIcon from "../../../assets/images/profile/copyIconNormal.svg";
import editIconHover from "../../../assets/images/profile/editHover.svg";
import editIcon from "../../../assets/images/profile/editNormal.svg";
import EditCardHoverIcon from "../../../components/ApproveEdit/EditCardHoverIcon";
import { Genders } from "../../../constants/enum";
import { DefaultPermissions } from "../../../constants/permissions";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { BankAccountPurpose } from "../../../models/DTOs/bankAccount/BankAccountDTO";
import { ApproveEmployeeEditDTO } from "../../../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeeEditDTO } from "../../../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import {
  approveEmployeeEdit,
  declineEmployeeEdit,
} from "../../../services/employees/employeesService";
import Translator from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";
import { useUserEmployee } from "../../UserEmployeeContext";

const FieldsetRow = styled(Container)`
  display: grid;
  grid-template-columns: 17% 25% 13% 30%;
  gap: 2rem;
  margin: 0.5rem 1rem;
`;

const StyledFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  width: 90%;
  margin: 1rem;
  padding: 1rem;
  position: relative;

  @media (max-width: 900px) {
    width: 100%;
    margin: 1rem 0;
  }
`;

const EmployeeImage = styled(Image)`
  border-radius: 50%;
  margin: 1rem;
`;

const EmployeeName = styled(Label)`
  text-align: center;
  font-weight: bold;
  font-size: 1.4rem;
  word-wrap: normal;
  margin-top: 0.5rem;
`;

const DepartmentInfo = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 1rem;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 2rem;
`;

const DepartmentName = styled(Label)`
  font-size: 1.15rem;
  margin-top: 0.2rem;
  color: var(--profile-department-name-font-color);
`;

const DepartmentLeader = styled(Label)`
  font-size: 1.05rem;
  color: var(--profile-department-leader-name-font-color);
  margin-top: 0.1rem;
`;

const ContractInfo = styled(Container)`
  display: flex;
  text-align: center;
  position: relative;
  top: 4rem;
  flex-direction: column;
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1.2rem;
` as unknown as React.FC<{ children: React.ReactNode }>;

const LeftContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 12rem;
  padding: 1rem;
  border-radius: 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;

  @media (max-width: 1200px) {
    width: 9rem;
    margin-left: -2.5rem;
  }
`;

const RightContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  flex: 1;

  @media (max-width: 1300px) {
    flex-direction: column;
    margin-left: 0;
    width: 100%;
  }
`;

const WrapperContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  width: 100%;
  padding: 1rem;
  margin: 0 auto;

  @media (max-width: 1200px) {
    width: 90%;
  }

  @media (max-width: 1000px) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
`;
const EditButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isDisabled: boolean;
}>`
  position: absolute;
  top: -1rem;
  right: 0.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background-color: transparent;
  background: no-repeat center / 1.6rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;

  &:hover {
    background: no-repeat center / 1.6rem url(${(p) => p.hoverImage});
  }
`;

const ButtonsContainer = styled(Container)`
  display: flex;
`;

const EditNameButton = styled(Button)<{
  normalImage: string;
  hoverImage: string;
  isDisabled?: boolean;
}>`
  width: 2.5rem;
  height: 2.5rem;
  background: no-repeat center / 1.8rem url(${(p) => p.normalImage});
  cursor: pointer;
  padding: 0;
  background-color: transparent;

  &:hover {
    background: no-repeat center / 1.8rem url(${(p) => p.hoverImage});
  }
`;

interface Props {
  incomingProfile: PersonalInformationDTO;
  employeeName: string;
}

const PersonalData = ({ incomingProfile, employeeName }: Props) => {
  const [profile, setProfile] = useState(incomingProfile);
  const { userEmployee } = useUserEmployee();
  const isApprover = userEmployee.permissions.includes(
    DefaultPermissions.Employees.Write
  );
  const prevoiusIban = profile?.employeePreviousValues?.bankAccounts.find(
    (account) => account.purpose.identifier === BankAccountPurpose.Salary
  )?.iban;
  const identityCardPreviousAddress =
    profile?.employeePreviousValues.addresses?.find(
      (address) =>
        address.purpose &&
        address.purpose.identifier === AddressPurpose.IdentityCard
    );
  const [identityCardAddress, setIdentityCardAddress] =
    useState<AddressDTO | null>();
  const { toggleMenu, changeView } = useMenu();

  function formatFullAddress(address?: AddressDTO | null) {
    if (!address) return "";
    const parts = [];
    if (address.neighborhood) parts.push(`${address.neighborhood},`);
    if (address.street) parts.push(` ${address.street}`);
    if (address.block) parts.push(`, ${address.block}`);
    if (address.apartment) parts.push(`, ${address.apartment}`);
    return parts.join(" ");
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, ".");
  };

  const handleEdit = (step: number) => {
    changeView("edit-employee", "other", { incomingProfile, step });
    toggleMenu();
  };

  const handleCopyPersonName = async () => {
    try {
      await navigator.clipboard.writeText(employeeName);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  useEffect(() => {
    setProfile(incomingProfile);
  }, [incomingProfile]);

  useEffect(() => {
    setIdentityCardAddress(
      incomingProfile?.addresses?.find(
        (address) =>
          address.purpose &&
          address.purpose.identifier === AddressPurpose.IdentityCard
      ) || null
    );
  }, [incomingProfile]);

  return (
    <WrapperContainer>
      <LeftContainer data-testid="profile-left-container">
        <EmployeeImage src={profileMan} data-testid="profile-employee-image" />
        <EmployeeName data-testid="profile-employee-name">
          {employeeName}
        </EmployeeName>
        <ButtonsContainer>
          <EditNameButton
            data-testid="copy-name-button"
            normalImage={copyIcon}
            hoverImage={copyIconHover}
            onClick={handleCopyPersonName}
            label=""
            isDisabled={true}
          />
          <EditNameButton
            data-testid="edit-company-button"
            normalImage={editIcon}
            hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            isDisabled={true}
          />
        </ButtonsContainer>
        <DepartmentInfo data-testid="profile-department-info">
          <DepartmentName data-testid="profile-department-name">
            Department Name
          </DepartmentName>
          <DepartmentLeader data-testid="profile-department-leader">
            Manager
          </DepartmentLeader>
        </DepartmentInfo>
        <ContractInfo data-testid="profile-contract-info"></ContractInfo>
      </LeftContainer>
      <RightContainer data-testid="profile-right-container">
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="personal-information-fieldset"
        >
          <EditButton
            data-testid="edit-company-button"
            normalImage={editIcon}
            hoverImage={editIconHover}
            onClick={() => handleEdit(1)}
            label=""
            isDisabled={true}
          />
          <Legend data-testid="information-legend">Personal Information</Legend>
          <FieldsetRow data-testid="first-row">
            <LabelColumn>
              <LightLabel>EGN / LNCH</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                isApprover &&
                profile?.employeePreviousValues?.egn !== profile?.employee?.egn
                  ? profile?.employeePreviousValues?.egn
                  : profile?.employee?.egn}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.egn !==
                  profile?.employee?.egn &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={profile?.employee?.egn}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "EGN",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "EGN",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>IBAN</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                isApprover &&
                prevoiusIban !== profile?.iban
                  ? prevoiusIban
                  : profile?.iban}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                prevoiusIban !== profile?.iban &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={profile?.iban}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IBAN",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "BankAccounts",
                          collectionItemId: profile.employee.bankAccounts.find(
                            (account) =>
                              account.purpose.identifier ===
                              BankAccountPurpose.Salary
                          )?.id,
                          propertyName: "IBAN",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="second-row">
            <LabelColumn>
              <LightLabel>Date of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {isApprover &&
                profile?.employeePreviousValues?.birthDate !==
                  profile?.employee?.birthDate
                  ? formatDate(profile?.employeePreviousValues?.birthDate)
                  : formatDate(profile?.employee?.birthDate)}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.birthDate !==
                  profile?.employee?.birthDate &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={formatDate(profile?.employee?.birthDate)}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthDate",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthDate",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>
                {profile?.employee?.email || profile?.employee?.email !== ""
                  ? "E-mail"
                  : "User number"}
              </LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employee?.email || profile?.employee?.email !== ""
                  ? profile?.employee?.email
                  : profile?.code}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="third-row">
            <LabelColumn>
              <LightLabel>Place of birth</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.birthPlace !==
                  profile?.employee?.birthPlace &&
                isApprover
                  ? profile?.employeePreviousValues?.birthPlace
                  : profile?.employee?.birthPlace}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.birthPlace !==
                  profile?.employee?.birthPlace &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={profile?.employee?.birthPlace}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthPlace",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "BirthPlace",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Phone number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.phone !==
                  profile?.employee?.phone &&
                isApprover
                  ? profile?.employeePreviousValues?.phone
                  : profile?.employee?.phone}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.phone !==
                  profile?.employee?.phone &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={profile?.employee?.phone}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Phone",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "Phone",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
        <StyledFieldset
          onSubmit={(e) => e.preventDefault()}
          data-testid="address-fieldset"
        >
          <Legend data-testid="address-legend">ID card details</Legend>
          <FieldsetRow data-testid="address-row">
            <EditButton
              data-testid="edit-company-button"
              normalImage={editIcon}
              hoverImage={editIconHover}
              onClick={() => handleEdit(2)}
              label=""
              isDisabled={true}
            />
            <LabelColumn>
              <LightLabel>Number</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.idNumber !==
                  profile?.employee?.idNumber &&
                isApprover
                  ? profile?.employeePreviousValues?.idNumber
                  : profile?.employee?.idNumber}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.idNumber !==
                  profile?.employee?.idNumber &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={profile?.employeePreviousValues?.idNumber}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDNumber",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDNumber",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>City</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                (identityCardAddress?.city?.name !==
                  identityCardPreviousAddress?.city?.name ||
                  identityCardAddress?.cityName !==
                    identityCardPreviousAddress?.cityName) &&
                isApprover
                  ? identityCardPreviousAddress?.city?.name ??
                    identityCardPreviousAddress?.cityName
                  : identityCardAddress?.city?.name ??
                    identityCardAddress?.cityName}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                (identityCardAddress?.city?.name !==
                  identityCardPreviousAddress?.city?.name ||
                  identityCardAddress?.cityName !==
                    identityCardPreviousAddress?.cityName) &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={
                      identityCardAddress?.city?.name ??
                      identityCardAddress?.cityName
                    }
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityId",
                        })
                      );
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityName",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityId",
                        })
                      );
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          collectionName: "Addresses",
                          collectionItemId: identityCardAddress?.id,
                          propertyName: "CityName",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued on</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.idIssueDate !==
                  profile?.employee?.idIssueDate &&
                isApprover
                  ? formatDate(profile?.employeePreviousValues?.idIssueDate)
                  : formatDate(profile?.employee?.idIssueDate)}
              </ValueLabel>
              {profile?.employeePreviousValues &&
                profile?.employeePreviousValues?.idIssueDate !==
                  profile?.employee?.idIssueDate &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={formatDate(
                      profile?.employeePreviousValues?.idIssueDate
                    )}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssueDate",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssueDate",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>District</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {identityCardAddress?.district?.name ??
                  identityCardAddress?.districtName}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Issued from</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {isApprover &&
                profile?.employeePreviousValues?.idIssuedFrom !==
                  profile?.employee?.idIssuedFrom ? (
                  <ValueLabel>
                    {profile?.employeePreviousValues?.idIssuedFrom ? (
                      <>
                        <Translator getString="MOI" />{" "}
                        {profile.employee.idIssuedFrom}
                      </>
                    ) : null}
                  </ValueLabel>
                ) : (
                  <ValueLabel>
                    {profile?.employee?.idIssuedFrom ? (
                      <>
                        <Translator getString="MOI" />{" "}
                        {profile.employee.idIssuedFrom}
                      </>
                    ) : null}
                  </ValueLabel>
                )}
              </ValueLabel>
              {profile?.employeePreviousValues?.idIssuedFrom !==
                profile?.employee?.idIssuedFrom &&
                (isApprover ? (
                  <EditCardHoverIcon
                    newValue={profile?.employee?.idIssuedFrom}
                    onConfirm={() => {
                      approveEmployeeEdit(
                        new ApproveEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssuedFrom",
                        })
                      );
                    }}
                    onCancel={() => {
                      declineEmployeeEdit(
                        new DeclineEmployeeEditDTO({
                          employeeId: profile.employee.workTimeId,
                          propertyName: "IDIssuedFrom",
                        })
                      );
                    }}
                  />
                ) : (
                  <></>
                ))}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Municipality</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {identityCardAddress?.municipality?.name ??
                  identityCardAddress?.municipalityName}
              </ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Citizenship</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {isApprover
                  ? profile?.employeePreviousValues?.citizenship ??
                    profile?.employee?.citizenship
                  : profile?.employee?.citizenship}
              </ValueLabel>
              {isApprover && profile?.employeePreviousValues?.citizenship ? (
                <EditCardHoverIcon
                  newValue={profile?.employeePreviousValues?.citizenship}
                  onConfirm={() => {
                    approveEmployeeEdit(
                      new ApproveEmployeeEditDTO({
                        employeeId: profile.employee.workTimeId,
                        propertyName: "citizenship",
                      })
                    );
                  }}
                  onCancel={() => {
                    declineEmployeeEdit(
                      new DeclineEmployeeEditDTO({
                        employeeId: profile.employee.workTimeId,
                        propertyName: "citizenship",
                      })
                    );
                  }}
                />
              ) : (
                <></>
              )}
            </ValueColumn>
            <LabelColumn>
              <LightLabel>Address</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>{formatFullAddress(identityCardAddress)}</ValueLabel>
            </ValueColumn>
          </FieldsetRow>
          <FieldsetRow data-testid="address-row">
            <LabelColumn>
              <LightLabel>Gender</LightLabel>
            </LabelColumn>
            <ValueColumn>
              <ValueLabel>
                {isApprover ? (
                  profile?.employeePreviousValues?.gender === Genders.Female ? (
                    <Translator getString="Female" />
                  ) : profile?.employeePreviousValues?.gender ===
                    Genders.Male ? (
                    <Translator getString="Male" />
                  ) : profile?.employee?.gender === Genders.Female ? (
                    <Translator getString="Female" />
                  ) : profile?.employee?.gender === Genders.Male ? (
                    <Translator getString="Male" />
                  ) : (
                    ""
                  )
                ) : profile?.employee?.gender === Genders.Female ? (
                  <Translator getString="Female" />
                ) : profile?.employee?.gender === Genders.Male ? (
                  <Translator getString="Male" />
                ) : (
                  ""
                )}
              </ValueLabel>
              {isApprover && profile?.employeePreviousValues?.gender ? (
                <EditCardHoverIcon
                  newValue={
                    profile?.employeePreviousValues?.gender === Genders.Female
                      ? "Female"
                      : profile?.employeePreviousValues?.gender === Genders.Male
                      ? "Male"
                      : ""
                  }
                  onConfirm={() => {
                    approveEmployeeEdit(
                      new ApproveEmployeeEditDTO({
                        employeeId: profile.employee.workTimeId,
                        propertyName: "gender",
                      })
                    );
                  }}
                  onCancel={() => {
                    declineEmployeeEdit(
                      new DeclineEmployeeEditDTO({
                        employeeId: profile.employee.workTimeId,
                        propertyName: "gender",
                      })
                    );
                  }}
                />
              ) : (
                <></>
              )}
            </ValueColumn>
          </FieldsetRow>
        </StyledFieldset>
      </RightContainer>
    </WrapperContainer>
  );
};

export default PersonalData;
