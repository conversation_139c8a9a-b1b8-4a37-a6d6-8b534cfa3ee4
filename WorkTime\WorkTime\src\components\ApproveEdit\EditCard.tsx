import React from "react";
import styled from "styled-components";
import OkIcon from "../../assets/images/dot-icons/ok.svg";
import XIcon from "../../assets/images/dot-icons/x.svg";
import Translator from "../../services/language/Translator";

interface EditCardProps {
  newValue: string | undefined;
  onCancel?: () => void;
  onConfirm?: () => void;
}

const CardContainer = styled.div`
  background-color: var(--auth-button-background-color);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 20rem;
  box-sizing: border-box;
`;

const HeaderText = styled.p`
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--combobox-header-text-color);
  font-weight: normal;
`;

const ValueText = styled.p`
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  color: var(--profile-department-name-font-color);
  font-weight: 500;
  line-height: 1.4;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.2rem;
`;

const ActionButton = styled.button<{ variant: "cancel" | "confirm" }>`
  background: none;
  border: 2px solid var(--profile-tab-label-border-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  padding: 0.5rem 2rem 0.5rem 1rem;
  border-radius: 4rem;
  transition: all 0.2s ease;

  &:hover,
  &:focus {
    border-color: ${(props) =>
      props.variant === "cancel"
        ? "var(--error-alert-color)"
        : "var(--success-alert-color)"};
  }
`;

const Icon = styled.img`
  width: 0.8rem;
  height: 0.8rem;
`;

const EditCard: React.FC<EditCardProps> = ({
  newValue,
  onCancel,
  onConfirm,
}) => {
  return (
    <CardContainer>
      <HeaderText>
        <Translator getString="New data:" />
      </HeaderText>
      <ValueText>{newValue}</ValueText>
      <ButtonContainer>
        <ActionButton
          variant="cancel"
          onClick={onCancel}
          data-testid="cancel-button"
        >
          <Icon src={XIcon} alt="Cancel" />
          <Translator getString="DeclineEdit" />
        </ActionButton>
        <ActionButton
          variant="confirm"
          onClick={onConfirm}
          data-testid="confirm-button"
        >
          <Icon src={OkIcon} alt="Confirm" />
          <Translator getString="Approve" />
        </ActionButton>
      </ButtonContainer>
    </CardContainer>
  );
};

export default EditCard;
