import { Genders } from "../../../constants/enum";
import { AddressDTO } from "../address/AddressDTO";
import { BankAccountDTO } from "../bankAccount/BankAccountDTO";
import { CompanyDTO } from "../companies/CompanyDTO";
import { IEntity } from "../IEntity";

export interface EmployeeDTO extends IEntity {
  id: string;
  workTimeId: string;
  firstName: string;
  secondName: string;
  lastName: string;
  egn: string;
  email: string;
  phone: string;
  workPhone: string;
  idNumber: string;
  idIssueDate: string;
  idIssuedFrom: string;
  gender: Genders;
  birthDate: string;
  birthPlace: string;
  citizenship: string;
  userId: string;
  companyId: string;
  company: CompanyDTO;
  bankAccounts: BankAccountDTO[];
  addresses: AddressDTO[];
}
