import { useEffect, useState } from "react";
import styled from "styled-components";

import { useParams } from "react-router-dom";
import Container from "../../../components/Container";
import Label from "../../../components/Inputs/Label";
import { AddressDTO } from "../../../models/DTOs/address/AddressDTO";
import { EmployeeDTO } from "../../../models/DTOs/employees/EmployeeDTO";
import { PersonalInformationDTO } from "../../../models/DTOs/payrolls/PersonalInformationDTO";
import { profileService } from "../../../services/profile/profileService";
import { useAuth } from "../../authentication/AuthContext";
import { useUserEmployee } from "../../UserEmployeeContext";
import Addresses from "./Addresses";
import PersonalData from "./PersonalData";

const MainContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 70%;
  height: 70%;
  padding: 1rem;
  margin: 0 auto;
`;

const TabLineContainer = styled(Container)`
  display: flex;
  flex-direction: row;
`;

const DataContainer = styled(Container)`
  display: flex;
  flex: 1;
  width: 100%;
  border-radius: 0 2.2rem 2.2rem 2.2rem;
  background-color: var(--profile-left-part-background-color);
  border: 0.3rem white solid;
  padding: 1rem;
  z-index: 5;
`;

const TabLabel = styled(Label)<{ active: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 4rem;
  width: 11rem;
  white-space: nowrap;
  background-color: var(--profile-tab-label-background-inactive);
  border-top-left-radius: 2.2rem;
  border-top-right-radius: 2.2rem;
  border-left: 0.3rem solid var(--profile-tab-label-border-color);
  border-right: 0.3rem solid var(--profile-tab-label-border-color);
  border-top: 0.3rem solid var(--profile-tab-label-border-color);
  z-index: 0;
  transition: background-color 0.1s ease-in-out, top 0.3s ease-in-out;

  padding-bottom: 0.6rem;
  color: white;

  top: ${({ active }) => (active ? "0.3rem" : "1rem")};

  ${({ active }) =>
    !active &&
    `
    &:hover {
      top: 0.3rem;
      background-color: var(--profile-tab-label-background-hover);
    }
  `}

  ${({ active }) =>
    active &&
    `
    color: var(--label-color);
    height: 4.1rem;
    border-top: 0.3rem white solid;
    border-left: 0.3rem white solid;
    border-right: 0.3rem white solid;
    background-color: var(--profile-left-part-background-color);
    z-index: 2;
  `}
  align-self: center;
  font-size: 1.4rem;

  @media (max-width: 900px) {
    font-size: 1rem;
    min-width: 6rem;
    padding: 0 0.3rem;
  }
`;

const Profile = () => {
  const { id, payrollId } = useParams();
  const { user } = useAuth();
  const { userEmployee } = useUserEmployee();
  const [profile, setProfile] = useState<PersonalInformationDTO>();
  const [activeTab, setActiveTab] = useState("personal");

  const loadProfile = async (id?: string, payrollId?: string) => {
    try {
      const loadedProfile = await profileService.onProfileLoaded(id, payrollId);

      setProfile(loadedProfile);
    } catch (error) {
      console.error("Error loading profile:", error);
    }
  };

  const saveProfile = async (profile: PersonalInformationDTO) => {
    try {
      await profileService.onProfileSaved(profile);
    } catch (error) {
      console.error("Error saving profile:", error);
    }
  };

  useEffect(() => {
    if (user?.userId) {
      loadProfile(id, payrollId);
    }
  }, [user?.userId, id, payrollId]);

  const handleTabClick = (tabName: string) => {
    setActiveTab(tabName);
  };

  const handleEditProfile = (incomingprofile: PersonalInformationDTO) => {
    saveProfile(incomingprofile);
  };

  const handleEditAddress = (incomingAddress: AddressDTO | null) => {};

  const renderName = (employee: EmployeeDTO | undefined) => {
    if (!employee) return "Unknown profile";

    let name = "";

    if (employee.firstName) {
      name += employee.firstName;
    }
    if (employee.secondName) {
      name += " " + employee.secondName;
    }
    if (employee.lastName) {
      name += " " + employee.lastName;
    }

    return name;
  };

  return (
    <MainContainer data-testid="profile-main-container">
      <TabLineContainer data-testid="profile-tab-line-container">
        <TabLabel
          data-testid="profile-tab-personal"
          active={activeTab === "personal"}
          onClick={() => handleTabClick("personal")}
        >
          Personal Data
        </TabLabel>
        <TabLabel
          data-testid="profile-tab-addresses"
          active={activeTab === "addresses"}
          onClick={() => handleTabClick("addresses")}
        >
          Addresses
        </TabLabel>
        <TabLabel
          data-testid="profile-tab-appointment"
          active={activeTab === "appointment"}
          onClick={() => handleTabClick("appointment")}
        >
          Appointment
        </TabLabel>
        <TabLabel
          data-testid="profile-tab-events"
          active={activeTab === "events"}
          onClick={() => handleTabClick("events")}
        >
          Events
        </TabLabel>
      </TabLineContainer>
      <DataContainer data-testid="profile-data-container">
        {activeTab === "personal" && profile && (
          <PersonalData
            employeeName={renderName(profile.employee)}
            data-testid="profile-personal-data"
            incomingProfile={profile}
          />
        )}
        {activeTab === "addresses" && profile && (
          <Addresses
            profile={profile}
            employeeName={renderName(profile.employee)}
            data-testid="profile-addresses"
          />
        )}
      </DataContainer>
    </MainContainer>
  );
};

export default Profile;
